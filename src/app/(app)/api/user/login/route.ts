import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import User<PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { UserDTO, User } from '@/app/(app)/_backend/common/dto/user/UserDTO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'
import TurnstileValidator from './turnstile'

export interface LoginRequest {
  email: string
  password: string
  captchaToken: string
}

export interface LoginResults {
  message: string
  data: User
}

export async function POST(request: Request) {
  try {
    const payload = await getPayload({ config })
    const { email, password, captchaToken } = (await request.json()) as LoginRequest
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    console.log(captchaToken)

    // test posthog-node usage
    const posthog = PostHogClient()
    posthog.capture({ event: 'password_login', distinctId: email })

    const validator = new TurnstileValidator(process.env.TURNSTILE_SECRET_KEY as string);
    const result = await validator.validate(captchaToken, undefined, {
      expectedAction: "login",
      expectedHostname: "localhost",
    });
    console.log(result)

    if (!result.success) {
      console.log("Validation failed:", result.error);
      return new Response(JSON.stringify({ message: 'Invalid captcha token!' }), { status: 401 })
    }

    const data = await userService.loginUser(email, password)
    const userDTO = new UserDTO(data.user)
    const modifiedUser = {
      ...data,
      user: userDTO.distil(),
    }
    const cookiesStore = await cookies()
    if (data?.token) {
      cookiesStore.set('la-token', data.token, {
        httpOnly: true,
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production',
      })
    }
    return new Response(JSON.stringify({ message: 'User is now logged in!', data: modifiedUser }))
  } catch (error) {
    return errorHandler(error)
  }
}
